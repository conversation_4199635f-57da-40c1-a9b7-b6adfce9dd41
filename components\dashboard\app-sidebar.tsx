"use client"

import * as React from "react"
import {
  IconPlug,
  IconPhone,
  IconDatabase,
  IconFrame,
  IconMap,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/dashboard/nav-documents"
import { NavMain } from "@/components/dashboard/nav-main"
import { NavSecondary } from "@/components/dashboard/nav-secondary"
import { NavUser } from "@/components/dashboard/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Agents",
      url: "/dashboard/agents",
      icon: IconUsers,
      isActive: false,
      items: [
        {
          title: "Overview",
          url: "/dashboard/agents/overview",
        },
        {
          title: "Customer Support Agent",
          url: "/dashboard/agents/customer-support",
        },
        {
          title: "Call Center Agent",
          url: "/dashboard/agents/call-center",
        },
      ],
    },
    {
      title: "RAG Knowledge Base",
      url: "/dashboard/rag",
      icon: IconDatabase,
      items: [
        {
          title: "Overview",
          url: "/dashboard/rag/overview",
        },
        {
          title: "Import",
          url: "/dashboard/rag/import",
        },
        {
          title: "Export",
          url: "/dashboard/rag/export",
        },
      ],
    },
    {
      title: "Integrations",
      url: "/dashboard/integrations",
      icon: IconPlug,
      items: [
        {
          title: "Overview",
          url: "/dashboard/integrations/overview",
        },
        {
          title: "Custom Integrations",
          url: "/dashboard/integrations/custom",
        },
        {
          title: "API",
          url: "/dashboard/integrations/api",
        },
        {
          title: "Third-party",
          url: "/dashboard/integrations/third-party",
        },
      ],
    },
    {
      title: "Phone numbers",
      url: "/dashboard/phone",
      icon: IconPhone,
      items: [
        {
          title: "Overview",
          url: "/dashboard/phone/overview",
        },
        {
          title: "Buy Phone numbers",
          url: "/dashboard/phone/buy",
        },
        {
          title: "Import your Phone number",
          url: "/dashboard/phone/import",
        },
      ],
    },
  ],
  Templates: [
    {
      name: "Medical",
      url: "#",
      icon: IconFrame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: IconFrame,
    },
    {
      name: "Customer Support",
      url: "#",
      icon: IconMap,
    },
  ],
  documents: [
    {
      name: "Introduction",
      url: "#",
      icon: IconFolder,
    },
    {
      name: "Get Started",
      url: "#",
      icon: IconFolder,
    },
    {
      name: "Tutorials",
      url: "#",
      icon: IconFolder,
    },
    {
      name: "Changelog",
      url: "#",
      icon: IconFolder,
    },
  ],
  navSecondary: [
    {
      title: "Support",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Feedback",
      url: "#",
      icon: IconReport,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">Acme Inc.</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
