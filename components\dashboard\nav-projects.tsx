"use client"

import {
  Folder,
  Forward,
  MoreH<PERSON><PERSON>tal,
  Trash2,
  type LucideIcon,
} from "lucide-react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

export function NavProjects({
  projects,
}: {
  projects: {
    name: string
    url: string
    icon: LucideIcon
  }[]
}) {
  return (
    <div>
      <h3 className="mb-2 text-sm font-medium text-muted-foreground px-2">Projects</h3>
      <div className="space-y-1">
        {projects.map((item) => (
          <div key={item.name} className="relative group">
            <Button
              variant="ghost"
              className="w-full justify-start gap-2 h-9"
              asChild
            >
              <a href={item.url}>
                <item.icon className="h-4 w-4" />
                <span>{item.name}</span>
              </a>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-7 w-7 p-0 opacity-0 group-hover:opacity-100"
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">More</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-48 rounded-lg"
                side="right"
                align="start"
              >
                <DropdownMenuItem>
                  <Folder className="text-muted-foreground" />
                  <span>View Project</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Forward className="text-muted-foreground" />
                  <span>Share Project</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Trash2 className="text-muted-foreground" />
                  <span>Delete Project</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ))}
        <Button
          variant="ghost"
          className="w-full justify-start gap-2 h-9 text-muted-foreground"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span>More</span>
        </Button>
      </div>
    </div>
  )
}
