{"name": "jasz-dashboard-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3010", "build": "next build", "start": "next start -p 3010", "lint": "next lint", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.0", "@tabler/icons-react": "^3.34.1", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-table": "^8.21.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotted-map": "^2.2.3", "framer-motion": "^12.6.3", "geist": "^1.3.1", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.479.0", "motion": "^12.6.3", "next": "^15.2.2", "next-themes": "^0.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.4", "sonner": "^2.0.1", "styled-components": "^6.1.17", "swiper": "^11.2.6", "tailwind-merge": "^3.1.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chai": "^5.2.2", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.7.0", "css-loader": "^7.1.2", "eslint": "^9", "eslint-config-next": "15.2.2", "jsdom": "^26.1.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.3", "postcss-import": "^16.1.1", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "tslib": "^2.8.1", "typescript": "^5", "vitest": "^3.2.4"}}