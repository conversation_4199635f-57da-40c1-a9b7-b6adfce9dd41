"use client"

import * as React from "react"
import {
  AudioWaveform,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Users,
  Database,
  Plug,
  Phone,
  ChevronLeft
} from "lucide-react"

// Importiere die Sidebar-Stile
import "./sidebar-styles.css"

import { NavMain } from "@/components/dashboard/nav-main"
import { NavTemplates } from "@/components/dashboard/nav-templates"
import { NavUser } from "@/components/dashboard/nav-user"
import { TeamSwitcher } from "@/components/dashboard/team-switcher"
// Shadcn Sidebar-Komponenten entfernt für einfaches Layout
import { Button } from "@/components/ui/button"

// This is sample data.
const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/default-avatar.jpg",
  },
  teams: [
    {
      name: "AI Assistant Technology",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "JASZ-AI",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Test.Corp",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "Agents",
      url: "/dashboard/agents",
      icon: Users,
      isActive: false,
      items: [
        {
          title: "Overview",
          url: "/dashboard/agents/overview",
        },
        {
          title: "Customer Support Agent",
          url: "/dashboard/agents/customer-support",
        },
        {
          title: "Call Center Agent",
          url: "/dashboard/agents/call-center",
        },
      ],
    },
    {
      title: "RAG Knowledge Base",
      url: "/dashboard/rag",
      icon: Database,
      items: [
        {
          title: "Overview",
          url: "/dashboard/rag/overview",
        },
        {
          title: "Import",
          url: "/dashboard/rag/import",
        },
        {
          title: "Export",
          url: "/dashboard/rag/export",
        },
      ],
    },
    {
      title: "Integrations",
      url: "/dashboard/integrations",
      icon: Plug,
      items: [
        {
          title: "Overview",
          url: "/dashboard/integrations/overview",
        },
        {
          title: "Custom Integrations",
          url: "/dashboard/integrations/custom",
        },
        {
          title: "API",
          url: "/dashboard/integrations/api",
        },
        {
          title: "Third-party",
          url: "/dashboard/integrations/third-party",
        },
      ],
    },
    {
      title: "Phone numbers",
      url: "/dashboard/phone",
      icon: Phone,
      items: [
        {
          title: "Overview",
          url: "/dashboard/phone/overview",
        },
        {
          title: "Buy Phone numbers",
          url: "/dashboard/phone/buy",
        },
        {
          title: "Import your Phone number",
          url: "/dashboard/phone/import",
        },
      ],
    },
  ],
  Templates: [
    {
      name: "Medical",
      url: "#",
      icon: Frame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: PieChart,
    },
    {
      name: "Customer Support",
      url: "#",
      icon: Map,
    },
  ],
}

// Einfache Sidebar ohne Shadcn Sidebar-Komponenten
export function DashboardSidebar({
  className
}: {
  className?: string;
}) {
  return (
    <div className={`h-full w-full bg-background border-r border-border flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        {/* Team-Switcher */}
        <div className="mb-4">
          <TeamSwitcher teams={data.teams} />
        </div>

        {/* Dashboard-Link */}
        <a
          href="/dashboard"
          className="dashboard-link flex items-center gap-3 px-4 py-4 rounded-md bg-[#4b5b63] hover:bg-[#7dd3fc]/20 text-white transition-colors">
          <GalleryVerticalEnd className="h-5 w-5" />
          <span>Dashboard</span>
        </a>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-2">
        <NavMain items={data.navMain} />
        <NavTemplates templates={data.Templates} />
      </div>

      {/* Footer */}
      <div className="p-2 border-t border-border">
        <NavUser user={data.user} />
      </div>
    </div>
  )
}

// Diese Beispielfunktion wird nur für Testzwecke verwendet
function DashboardPageExample() {
  return (
    <main className="flex min-h-screen bg-black text-white">
      <DashboardSidebar />
    </main>
  )
}
